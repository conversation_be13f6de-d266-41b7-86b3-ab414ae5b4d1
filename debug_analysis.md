# MPU 调试分析

## 问题现象
- 模块加载成功，但 `mpu_hooked_ioctl` 函数没有被调用
- 说明 syscall hook 层面存在问题

## 可能的原因

### 1. sys_call_table 获取失败
在较新的内核版本中，`sys_call_table` 可能：
- 无法通过 `kallsyms_lookup_name` 获取
- 获取到的地址不正确
- 被内核保护机制阻止修改

### 2. 内核版本兼容性
- 内核 5.7+ 版本对 `kallsyms_lookup_name` 的访问有限制
- syscall wrapper 机制的变化
- 内存保护机制的增强

### 3. 架构相关问题
- ARM64 vs x86_64 的差异
- 不同架构的 syscall 调用约定

## 调试步骤

### 1. 检查初始化日志
运行模块后检查 `dmesg` 输出：
```bash
dmesg | grep mpu
```

应该看到：
- "Starting ioctl hook initialization"
- "kallsyms_lookup_name found at ..."
- "sys_call_table found at ..."
- "Original ioctl at ..."
- "ioctl hook installed successfully"

### 2. 检查 hook 是否被调用
运行任何会触发 ioctl 的程序，然后检查：
```bash
dmesg | grep "hooked ioctl called"
```

### 3. 如果 hook 没有被调用
可能的原因：
- syscall table 修改失败
- 获取到错误的 syscall table 地址
- 内核保护机制阻止了修改

## 解决方案

### 方案 1: 增强调试信息
已在代码中添加了详细的调试信息，包括：
- 初始化过程的每一步
- 地址信息
- 错误状态

### 方案 2: kprobe 备用方案
如果 syscall table hook 失败，使用 kprobe 方式：
- 监控 ioctl 调用
- 虽然不能完全替换，但可以验证调用

### 方案 3: 其他 hook 方法
- ftrace
- eBPF (如果内核支持)
- 直接修改函数入口点

## 测试建议

1. 在 Linux 环境中运行 `test_debug.sh`
2. 仔细检查所有日志输出
3. 如果 syscall table hook 失败，kprobe 备用方案会自动启用
4. 比较不同内核版本的行为

## 常见问题

### Q: 为什么在 macOS 上无法测试？
A: 这是 Linux 内核模块，只能在 Linux 环境中编译和运行。

### Q: 如果所有方法都失败怎么办？
A: 可能需要：
- 使用更新的 hook 技术
- 针对特定内核版本优化
- 考虑用户空间解决方案

### Q: 如何确认 hook 是否工作？
A: 运行任何使用 ioctl 的程序（如 nvidia-smi），然后检查内核日志中是否有 "hooked ioctl called" 消息。
