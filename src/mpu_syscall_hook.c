//  MPU, A shim driver allows in-docker nvidia-smi showing correct process list without modify anything
//  Copyright (C) 2021, Matpool
//
//  This program is free software; you can redistribute it and/or modify
//  it under the terms of the GNU General Public License as published by
//  the Free Software Foundation; either version 2 of the License, or
//  (at your option) any later version.
//
//  This program is distributed in the hope that it will be useful,
//  but WITHOUT ANY WARRANTY; without even the implied warranty of
//  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
//  GNU General Public License for more details.
//
//  You should have received a copy of the GNU General Public License along
//  with this program; if not, write to the Free Software Foundation, Inc.,
//  51 Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA.

#include "mpu_syscall_hook.h"
#include <linux/module.h>
#include <linux/kprobes.h>
#include <linux/kallsyms.h>
#include <linux/version.h>

#include <linux/syscalls.h>
#include <linux/cgroup.h>
#include <asm/paravirt.h>
#include <linux/slab.h>
#include <linux/file.h>

#if (LINUX_VERSION_CODE >= KERNEL_VERSION(3,9,0))
#define FILE_INODE(f) (f->f_inode)
#else
#define FILE_INODE(f) (f->f_path.dentry->d_inode)
#endif // FILE_INODE

// kernel 4.17 introduces syscall wrapper
#ifdef CONFIG_ARCH_HAS_SYSCALL_WRAPPER
typedef asmlinkage long (*ioctl_fn)(const struct pt_regs *);

typedef struct mpu_ioctl_private_s
{
  mpu_ioctl_call_t c;
  ioctl_fn ioctl;
  const struct pt_regs *regs;
} mpu_ioctl_private_t;

static asmlinkage long mpu_hooked_ioctl(const struct pt_regs *regs);
#else
typedef asmlinkage long (*ioctl_fn)(unsigned int fd, unsigned int cmd, unsigned long arg);

typedef struct mpu_ioctl_private_s
{
  mpu_ioctl_call_t c;
  ioctl_fn ioctl;
} mpu_ioctl_private_t;

static asmlinkage long mpu_hooked_ioctl(unsigned int fd, unsigned int cmd, unsigned long arg);
#endif // CONFIG_ARCH_HAS_SYSCALL_WRAPPER

// all its fields are immutable without ownership
typedef struct mpu_syscall_hook_s
{
  mpu_module_t *module;
  mpu_ctx_t *ctx;
  unsigned long **syscall_tbl;
  ioctl_fn ioctl;
} mpu_syscall_hook_t;

static mpu_syscall_hook_t mpu_hook_instance;

static dev_t get_rdev(unsigned int fd)
{
  struct fd f = fdget(fd);
  struct inode *inode;
  dev_t rdev;

  if (f.file)
  {
    inode = FILE_INODE(f.file);
    if (inode)
    {
      rdev = inode->i_rdev;
    }
    fdput(f);
  }
  return rdev;
}

#if LINUX_VERSION_CODE >= KERNEL_VERSION(5, 3, 0)
static inline void write_cr0_forced(unsigned long val)
{
	unsigned long __force_order;

	asm volatile(
		"mov %0, %%cr0"
		: "+r"(val), "+m"(__force_order));
}
#define WRITE_CR0(f) write_cr0_forced(f)
#else
#define WRITE_CR0(f) write_cr0(f)
#endif

static void write_syscall(unsigned long **syscall_tbl, ioctl_fn sys_ioctl)
{
  unsigned long local_cr0;

  printk(KERN_DEBUG "mpu: Attempting to write syscall table entry\n");
  printk(KERN_DEBUG "mpu: syscall_tbl=%p, __NR_ioctl=%d, new_ioctl=%p\n",
         syscall_tbl, __NR_ioctl, sys_ioctl);

  local_cr0 = read_cr0();
  printk(KERN_DEBUG "mpu: Original CR0: 0x%lx\n", local_cr0);

  WRITE_CR0(local_cr0 & ~0x00010000);
  syscall_tbl[__NR_ioctl] = (unsigned long *)sys_ioctl;
  WRITE_CR0(local_cr0);

  printk(KERN_DEBUG "mpu: Syscall table entry updated\n");
}

typedef unsigned long (*kallsyms_lookup_name_t)(const char *name);
static kallsyms_lookup_name_t my_kallsyms_lookup_name;

static struct kprobe kp = {
        .symbol_name = "kallsyms_lookup_name"
};

// 备用方案：使用 kprobe hook ioctl
static struct kprobe ioctl_kp;
static int use_kprobe_fallback = 0;

static int ioctl_kprobe_pre_handler(struct kprobe *p, struct pt_regs *regs)
{
    unsigned int fd, cmd;
    unsigned long arg;
    dev_t dev;

#ifdef CONFIG_ARCH_HAS_SYSCALL_WRAPPER
    fd = (unsigned int)regs->di;
    cmd = (unsigned int)regs->si;
    arg = (unsigned long)regs->dx;
#else
    fd = (unsigned int)regs->di;
    cmd = (unsigned int)regs->si;
    arg = (unsigned long)regs->dx;
#endif

    printk(KERN_DEBUG "mpu: kprobe ioctl handler called, fd=%u, cmd=0x%x\n", fd, cmd);

    dev = get_rdev(fd);
    printk(KERN_DEBUG "mpu: device major=%d, minor=%d\n", MAJOR(dev), MINOR(dev));

    // 这里可以调用我们的处理逻辑
    // 但是 kprobe 不能替换返回值，只能监控

    return 0;
}

static int init_kprobe_fallback(void)
{
    int ret;

    printk(KERN_INFO "mpu: Trying kprobe fallback method\n");

    // 尝试不同的 ioctl 符号名
    const char *ioctl_symbols[] = {
        "__x64_sys_ioctl",
        "sys_ioctl",
        "__se_sys_ioctl",
        NULL
    };

    int i;
    for (i = 0; ioctl_symbols[i]; i++) {
        ioctl_kp.symbol_name = ioctl_symbols[i];
        ioctl_kp.pre_handler = ioctl_kprobe_pre_handler;

        ret = register_kprobe(&ioctl_kp);
        if (ret == 0) {
            printk(KERN_INFO "mpu: Successfully registered kprobe on %s\n", ioctl_symbols[i]);
            use_kprobe_fallback = 1;
            return 0;
        }
        printk(KERN_DEBUG "mpu: Failed to register kprobe on %s: %d\n", ioctl_symbols[i], ret);
    }

    printk(KERN_ERR "mpu: All kprobe attempts failed\n");
    return -1;
}

int mpu_init_ioctl_hook(mpu_module_t *module, mpu_ctx_t *ctx)
{
  unsigned long **syscall_tbl;
  ioctl_fn sys_ioctl;

  printk(KERN_INFO "mpu: Starting ioctl hook initialization\n");

  if (!module || !module->ioctl || !ctx)
  {
    printk(KERN_ERR "mpu: Invalid parameters for hook init\n");
    return -EINVAL;
  }

  int ret;
#if LINUX_VERSION_CODE >= KERNEL_VERSION(5,7,0)
    printk(KERN_INFO "mpu: Using kprobe method for kallsyms_lookup_name (kernel >= 5.7)\n");
    ret = register_kprobe(&kp);
    if (ret < 0) {
        printk(KERN_ERR "mpu: kprobe register failed: %d\n", ret);
        return ret;
    }
    my_kallsyms_lookup_name = (kallsyms_lookup_name_t)kp.addr;
    unregister_kprobe(&kp);
    printk(KERN_INFO "mpu: kallsyms_lookup_name found at %p\n", my_kallsyms_lookup_name);
#else
    my_kallsyms_lookup_name = kallsyms_lookup_name;
    printk(KERN_INFO "mpu: Using direct kallsyms_lookup_name (kernel < 5.7)\n");
#endif

    if (!my_kallsyms_lookup_name) {
        printk(KERN_ERR "mpu: kallsyms_lookup_name not found\n");
        return -ENOENT;
    }

    syscall_tbl = (unsigned long **)my_kallsyms_lookup_name("sys_call_table");
    printk(KERN_INFO "mpu: sys_call_table found at %p\n", syscall_tbl);

  if (!syscall_tbl)
  {
    printk(KERN_ERR "mpu: Failed to find sys_call_table\n");
    return -ENXIO;
  }

  sys_ioctl = (ioctl_fn)syscall_tbl[__NR_ioctl];
  printk(KERN_INFO "mpu: Original ioctl at %p, __NR_ioctl=%d\n", sys_ioctl, __NR_ioctl);

  mpu_hook_instance.module = module;
  mpu_hook_instance.ctx = ctx;
  mpu_hook_instance.syscall_tbl = syscall_tbl;
  mpu_hook_instance.ioctl = sys_ioctl;

  barrier();
  write_syscall(syscall_tbl, mpu_hooked_ioctl);

  printk(KERN_INFO "mpu: ioctl hook installed successfully\n");
  return 0;
}

// if module is un-loaded but still retain hooked ioctl address
// the kernel will be panic
void mpu_exit_ioctl_hook(void)
{
  if (mpu_hook_instance.syscall_tbl && mpu_hook_instance.ioctl)
  {
    write_syscall(mpu_hook_instance.syscall_tbl, mpu_hook_instance.ioctl);
  }
}

#ifdef CONFIG_ARCH_HAS_SYSCALL_WRAPPER
static asmlinkage long mpu_hooked_ioctl(const struct pt_regs *regs)
{
  printk(KERN_DEBUG "mpu: hooked ioctl called (syscall wrapper), fd=%u, cmd=0x%x\n",
         (unsigned int)regs->di, (unsigned int)regs->si);

  mpu_ioctl_private_t pc = {
      .c = {
          .fd = (unsigned int)regs->di,
          .cmd = (unsigned int)regs->si,
          .arg = (unsigned long)regs->dx,
      },
      .ioctl = mpu_hook_instance.ioctl,
      .regs = regs,
  };
  dev_t dev = get_rdev(pc.c.fd);

  printk(KERN_DEBUG "mpu: device major=%d, minor=%d\n", MAJOR(dev), MINOR(dev));

  return mpu_hook_instance.module->ioctl(mpu_hook_instance.ctx, &pc.c, dev);
}

long mpu_call_ioctl(mpu_ioctl_call_t *c)
{
  mpu_ioctl_private_t *pc = container_of(c, mpu_ioctl_private_t, c);
  return pc->ioctl(pc->regs);
}
#else
static asmlinkage long mpu_hooked_ioctl(unsigned int fd, unsigned int cmd, unsigned long arg)
{
  printk(KERN_DEBUG "mpu: hooked ioctl called (no wrapper), fd=%u, cmd=0x%x\n", fd, cmd);

  mpu_ioctl_private_t pc = {
      .c = {
          .fd = fd,
          .cmd = cmd,
          .arg = arg,
      },
      .ioctl = mpu_hook_instance.ioctl,
  };
  dev_t dev = get_rdev(fd);

  printk(KERN_DEBUG "mpu: device major=%d, minor=%d\n", MAJOR(dev), MINOR(dev));

  return mpu_hook_instance.module->ioctl(mpu_hook_instance.ctx, &pc.c, dev);
}

long mpu_call_ioctl(mpu_ioctl_call_t *c)
{
  mpu_ioctl_private_t *pc = container_of(c, mpu_ioctl_private_t, c);
  return pc->ioctl(c->fd, c->cmd, c->arg);
}
#endif // CONFIG_ARCH_HAS_SYSCALL_WRAPPER