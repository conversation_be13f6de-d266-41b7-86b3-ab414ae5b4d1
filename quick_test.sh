#!/bin/bash

# 快速测试脚本 - 专门用于测试 hook 是否工作

set -e

echo "=== MPU Hook 快速测试 ==="

# 检查权限
if [[ $EUID -ne 0 ]]; then
    echo "错误: 需要 root 权限"
    exit 1
fi

# 清理之前的模块
echo "清理之前的模块..."
rmmod mpu 2>/dev/null || true

# 清理内核日志
dmesg -C

# 编译
echo "编译模块..."
make clean && make

# 加载模块
echo "加载模块..."
insmod mpu.ko

# 等待初始化
sleep 1

echo "=== 初始化日志 ==="
dmesg | grep mpu

echo ""
echo "=== 测试 ioctl 调用 ==="

# 创建简单的测试程序
cat > /tmp/simple_ioctl.c << 'EOF'
#include <stdio.h>
#include <fcntl.h>
#include <unistd.h>
#include <sys/ioctl.h>

int main() {
    int fd = open("/dev/null", O_RDONLY);
    if (fd >= 0) {
        printf("Calling ioctl...\n");
        ioctl(fd, 0x1234, 0);
        close(fd);
        printf("ioctl completed\n");
    }
    return 0;
}
EOF

gcc -o /tmp/simple_ioctl /tmp/simple_ioctl.c
/tmp/simple_ioctl

echo ""
echo "=== 检查 Hook 日志 ==="
if dmesg | grep -q "HOOKED IOCTL CALLED"; then
    echo "✅ SUCCESS: Hook 工作正常!"
    dmesg | grep "HOOKED IOCTL CALLED"
else
    echo "❌ FAILED: Hook 没有被调用"
    echo "完整的 mpu 日志:"
    dmesg | grep mpu
fi

# 清理
rm -f /tmp/simple_ioctl.c /tmp/simple_ioctl

echo ""
echo "=== 卸载模块 ==="
rmmod mpu
dmesg | grep mpu | tail -5

echo "测试完成"
