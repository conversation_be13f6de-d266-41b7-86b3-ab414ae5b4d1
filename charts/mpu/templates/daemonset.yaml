kind: DaemonSet
apiVersion: apps/v1
metadata:
  name: {{ include "mpu.fullname" . }}
  labels:
    {{- include "mpu.labels" . | nindent 4 }}
spec:
  selector:
    matchLabels:
      {{- include "mpu.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "mpu.labels" . | nindent 8 }}
          {{- with .Values.podLabels }}
          {{- toYaml . | nindent 8 }}
          {{- end }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      containers:
        - name: {{ .Chart.Name }}
          image: "{{ .Values.image.registry }}/{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          securityContext:
            privileged: true
            seLinuxOptions:
              level: s0
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}