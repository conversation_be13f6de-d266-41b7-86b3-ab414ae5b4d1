#!/bin/bash

# MPU 调试测试脚本
# 用于在 Linux 环境中测试和调试 MPU 模块

set -e

echo "=== MPU Debug Test Script ==="

# 检查是否在 Linux 环境
if [[ "$(uname)" != "Linux" ]]; then
    echo "错误: 此脚本需要在 Linux 环境中运行"
    exit 1
fi

# 检查是否有 root 权限
if [[ $EUID -ne 0 ]]; then
    echo "错误: 需要 root 权限来加载内核模块"
    exit 1
fi

# 清理之前的模块
echo "清理之前的模块..."
rmmod mpu 2>/dev/null || true

# 清理内核日志
echo "清理内核日志..."
dmesg -C

# 编译模块
echo "编译模块..."
make clean
make

if [[ ! -f mpu.ko ]]; then
    echo "错误: 模块编译失败"
    exit 1
fi

echo "模块编译成功"

# 加载模块
echo "加载模块..."
insmod mpu.ko

# 等待一下让模块初始化
sleep 1

# 检查模块是否加载成功
if lsmod | grep -q mpu; then
    echo "模块加载成功"
else
    echo "错误: 模块加载失败"
    dmesg | tail -20
    exit 1
fi

# 显示初始化日志
echo "=== 初始化日志 ==="
dmesg | grep mpu | tail -20

# 测试 nvidia-smi
echo "=== 测试 nvidia-smi ==="
if command -v nvidia-smi >/dev/null 2>&1; then
    echo "运行 nvidia-smi..."
    timeout 10s nvidia-smi || echo "nvidia-smi 超时或失败"
    
    echo "=== Hook 调试日志 ==="
    dmesg | grep mpu | tail -30
else
    echo "nvidia-smi 未找到，尝试其他 ioctl 调用..."
    
    # 创建一个简单的测试程序来触发 ioctl
    cat > /tmp/test_ioctl.c << 'EOF'
#include <stdio.h>
#include <fcntl.h>
#include <unistd.h>
#include <sys/ioctl.h>
#include <termios.h>

int main() {
    printf("=== Testing various ioctl calls ===\n");

    // 测试 1: /dev/null
    int fd1 = open("/dev/null", O_RDONLY);
    if (fd1 >= 0) {
        printf("Testing ioctl on /dev/null (fd=%d)...\n", fd1);
        ioctl(fd1, 0x1234, 0);
        close(fd1);
    }

    // 测试 2: /dev/zero
    int fd2 = open("/dev/zero", O_RDONLY);
    if (fd2 >= 0) {
        printf("Testing ioctl on /dev/zero (fd=%d)...\n", fd2);
        ioctl(fd2, 0x5678, 0);
        close(fd2);
    }

    // 测试 3: 标准输入 (通常是终端)
    printf("Testing ioctl on stdin (fd=0)...\n");
    struct termios term;
    tcgetattr(0, &term);  // 这会触发 ioctl

    // 测试 4: 直接的 ioctl 调用
    printf("Testing direct ioctl calls...\n");
    ioctl(0, TCGETS, &term);
    ioctl(1, TCGETS, &term);

    printf("All ioctl tests completed\n");
    return 0;
}
EOF

    gcc -o /tmp/test_ioctl /tmp/test_ioctl.c
    echo "Running ioctl test program..."
    /tmp/test_ioctl
    
    echo "=== Hook 调试日志 ==="
    dmesg | grep mpu | tail -30
fi

echo "=== 测试完成 ==="
echo "如果没有看到 'hooked ioctl called' 消息，说明 hook 没有工作"
echo "请检查上面的日志输出"

# 卸载模块
echo "卸载模块..."
rmmod mpu

echo "=== 卸载日志 ==="
dmesg | grep mpu | tail -10

# 清理临时文件
rm -f /tmp/test_ioctl.c /tmp/test_ioctl

echo "测试脚本完成"
