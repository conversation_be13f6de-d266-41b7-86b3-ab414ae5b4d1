name: Dev Build image And Package Helm charts

on:
  push:
    branches:
      - main

env:
  REGISTRY: ghcr.io
  REGISTRY_PATH: ${{ github.repository }}
  REGISTRY_USER: ${{ github.repository_owner }}
  CHARTS_DIR: charts/mpu
  UNSTABLE_CHARTS: 0.0.0

jobs:
  build-and-publish-images:
    runs-on: ubuntu-20.04
    permissions:
      packages: write
      contents: read

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Install Helm
        uses: azure/setup-helm@v4.0.0

      - name: Log in to registry
        run: |
          echo "${{ secrets.GITHUB_TOKEN }}" | \
              docker login ${{ env.REGISTRY }} -u ${{ env.REGISTRY_USER }} --password-stdin
      - name: Build images
        env:
          IMAGE_VERSION: "dev"
        run: |
          make images

      - name: Push images
        env:
          IMAGE_VERSION: "dev"
        run: |
          docker push ${{ env.REGISTRY }}/${{ env.REGISTRY_PATH }}:${{ env.IMAGE_VERSION }}

      - name: Log In To Registry
        run: |
          echo "${{ secrets.GITHUB_TOKEN }}" | \
              helm registry login ${{ env.REGISTRY }}/${{ env.REGISTRY_PATH }} -u ${{ env.REGISTRY_USER }} --password-stdin

      - name: Package Unstable Helm Charts
        id: package-charts
        run: |
          helm package --version ${{ env.UNSTABLE_CHARTS }} "$CHARTS_DIR"

      - name: Push Unstable Helm Charts To Registry
        shell: bash
        run: |
          helm push mpu-${{ env.UNSTABLE_CHARTS }}.tgz oci://${{ env.REGISTRY }}/${{ env.REGISTRY_USER }}