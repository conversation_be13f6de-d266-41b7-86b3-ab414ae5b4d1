name: helm linter

on:
  push:
    branches:
      - main
    tags:
      - v*

jobs:
  lint-test:
    runs-on: ubuntu-20.04
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up <PERSON><PERSON>
        uses: azure/setup-helm@v4.0.0
        with:
          version: v3.11.2

      - name: Run linter for all the charts
        run: helm lint charts/*