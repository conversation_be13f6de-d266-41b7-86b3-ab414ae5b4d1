name: Release Build image And Package Helm charts

on:
  push:
    tags:
      - v*


env:
  REGISTRY: ghcr.io
  REGISTRY_USER: ${{ github.repository_owner }}
  REGISTRY_PATH: ${{ github.repository }}
  CHARTS_DIR: charts/mpu

jobs:
  build-and-publish-images:
    runs-on: ubuntu-20.04
    permissions:
      packages: write
      contents: read

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Install Helm
        uses: azure/setup-helm@v4.0.0

      - name: Log in to registry
        run: |
          echo "${{ secrets.GITHUB_TOKEN }}" | \
              docker login ${{ env.REGISTRY }} -u ${{ env.REGISTRY_USER }} --password-stdin
      - name: Build images
        run: |
          GITREF=${{ github.ref }}
          echo "  - digging out tag from git ref $GITREF..."
          case $GITREF in
              refs/tags/v*)
                  tag="${GITREF#refs/tags/}"
                  ;;
              refs/heads/main)
                  tag=unstable
                  ;;
              refs/heads/release-*)
                  tag="${GITREF#refs/heads/release-}-unstable"
                  ;;
              *)
                  echo "error: can't determine tag."
                  exit 1
                  ;;
          esac
          echo "  - tag: $tag"
          IMAGE_VERSION=$tag make images

      - name: Push images
        run: |
          GITREF=${{ github.ref }}
          echo "  - digging out tag from git ref $GITREF..."
          case $GITREF in
              refs/tags/v*)
                  tag="${GITREF#refs/tags/}"
                  ;;
              refs/heads/main)
                  tag=unstable
                  ;;
              refs/heads/release-*)
                  tag="${GITREF#refs/heads/release-}-unstable"
                  ;;
              *)
                  echo "error: can't determine tag."
                  exit 1
                  ;;
          esac
          echo "  - tag: $tag" 
          docker push ${{ env.REGISTRY }}/${{ env.REGISTRY_PATH }}:$tag


      - name: Log In To Registry
        run: |
          echo "${{ secrets.GITHUB_TOKEN }}" | \
              helm registry login ${{ env.REGISTRY }}/${{ env.REGISTRY_PATH }} -u ${{ env.REGISTRY_USER }} --password-stdin

      - name: Package Unstable Helm Charts
        id: package-charts
        run: |
          helm package "$CHARTS_DIR"

      - name: Push Unstable Helm Charts To Registry
        shell: bash
        run: |
          GITREF=${{ github.ref }}
          echo "  - digging out tag from git ref $GITREF..."
          case $GITREF in
              refs/tags/v*)
                  tag="${GITREF#refs/tags/}"
                  ;;
              refs/heads/main)
                  tag=unstable
                  ;;
              refs/heads/release-*)
                  tag="${GITREF#refs/heads/release-}-unstable"
                  ;;
              *)
                  echo "error: can't determine tag."
                  exit 1
                  ;;
          esac
          echo "  - tag: $tag" 
          new_version=${tag#v}
          helm push mpu-$new_version.tgz oci://${{ env.REGISTRY }}/${{ env.REGISTRY_USER }}